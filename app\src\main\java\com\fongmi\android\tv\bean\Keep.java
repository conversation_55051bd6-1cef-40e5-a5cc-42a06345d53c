package com.fongmi.android.tv.bean;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.fongmi.android.tv.App;
import com.fongmi.android.tv.api.config.VodConfig;
import com.fongmi.android.tv.db.AppDatabase;
import com.fongmi.android.tv.event.RefreshEvent;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.List;

@Entity
public class Keep {

    @NonNull
    @PrimaryKey
    @SerializedName("key")
    private String key;
    @SerializedName("siteName")
    private String siteName;
    @SerializedName("vodName")
    private String vodName;
    @SerializedName("vodPic")
    private String vodPic;
    @SerializedName("createTime")
    private long createTime;
    @SerializedName("type")
    private int type;
    @SerializedName("cid")
    private int cid;

    public static List<Keep> arrayFrom(String str) {
        Type listType = new TypeToken<List<Keep>>() {
        }.getType();
        List<Keep> items = App.gson().fromJson(str, listType);
        return items == null ? Collections.emptyList() : items;
    }

    @NonNull
    public String getKey() {
        return key;
    }

    public void setKey(@NonNull String key) {
        this.key = key;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getVodName() {
        return vodName;
    }

    public void setVodName(String vodName) {
        this.vodName = vodName;
    }

    public String getVodPic() {
        return vodPic;
    }

    public void setVodPic(String vodPic) {
        this.vodPic = vodPic;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getCid() {
        return cid;
    }

    public void setCid(int cid) {
        this.cid = cid;
    }

    public String getSiteKey() {
        return getKey().split(AppDatabase.SYMBOL)[0];
    }

    public String getVodId() {
        return getKey().split(AppDatabase.SYMBOL)[1];
    }

    public static Keep find(String key) {
        return find(VodConfig.getCid(), key);
    }

    public static Keep find(int cid, String key) {
        return AppDatabase.get().getKeepDao().find(cid, key);
    }

    public static boolean exist(String key) {
        return AppDatabase.get().getKeepDao().find(key) != null;
    }

    public static void deleteAll() {
        AppDatabase.get().getKeepDao().delete();
    }

    public static void delete(int cid) {
        AppDatabase.get().getKeepDao().delete(cid);
    }

    public static void delete(String key) {
        AppDatabase.get().getKeepDao().delete(key);
    }

    public static List<Keep> getVod() {
        return AppDatabase.get().getKeepDao().getVod();
    }

    public static List<Keep> getLive() {
        return AppDatabase.get().getKeepDao().getLive();
    }

    public void save(int cid) {
        setCid(cid);
        AppDatabase.get().getKeepDao().insertOrUpdate(this);
    }

    public void save() {
        android.util.Log.i("VOD_FLOW", String.format("[KEEP_SAVE] 保存收藏记录到数据库 Key: %s, 电影: %s, 站点: %d, 站点名: %s, 海报: %s",
                getKey(), getVodName(), getCid(), getSiteName(), getVodPic()));
        AppDatabase.get().getKeepDao().insertOrUpdate(this);
        android.util.Log.i("VOD_FLOW", String.format("[KEEP_SAVE_SUCCESS] 收藏记录保存成功 Key: %s", getKey()));
    }

    public Keep delete() {
        android.util.Log.i("VOD_FLOW", String.format("[KEEP_DELETE] 删除收藏记录 Key: %s, 电影: %s, 站点: %d",
                getKey(), getVodName(), getCid()));
        AppDatabase.get().getKeepDao().delete(getCid(), getKey());
        android.util.Log.i("VOD_FLOW", String.format("[KEEP_DELETE_SUCCESS] 收藏记录删除成功 Key: %s", getKey()));
        return this;
    }

    private static void startSync(List<Config> configs, List<Keep> targets) {
        for (Keep target : targets) {
            for (Config config : configs) {
                if (target.getCid() == config.getId()) {
                    target.save(Config.find(config).getId());
                }
            }
        }
    }

    public static void sync(List<Config> configs, List<Keep> targets) {
        App.execute(() -> {
            startSync(configs, targets);
            RefreshEvent.keep();
        });
    }
}
